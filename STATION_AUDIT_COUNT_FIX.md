# 数据质量监控统计结果不一致问题修复

## 问题描述

用户反馈两个API接口的统计结果不一致：
- 公司侧统计接口：`/dataService/service/dataQuality-new/station/audit/count`
- 厂站统计接口：`/dataService/service/dataQuality-new/station/audit/delay/systemCount`

具体表现为：葛洲坝在厂站统计中显示413个测点，但在公司侧统计中数量不一致。

## 问题分析

通过代码分析发现，两个接口的统计逻辑存在差异：

### 1. 公司侧统计接口 (`/count`)
- **实现类**: `DataQualityStationAuditServiceImpl.count()`
- **数据来源**: 合并两个表的统计结果
  - `dq_new_all_station` (未管理测点统计)
  - `dq_new_station_delay` (延迟测点统计)
- **SQL查询** (延迟测点部分):
```sql
SELECT t2.id as siteId, ifnull(t2.site_name, '综合') as siteName, 
       count(distinct(t1.station_number)) as "count"
FROM dq_new_station_delay t1
LEFT JOIN bcs_sites t2 ON t1.site_id = t2.id
WHERE t1.delay_state = 1
GROUP BY t2.id, t2.site_name
```

### 2. 厂站统计接口 (`/delay/systemCount`)
- **实现类**: `DataQualityStationDelayServiceImpl.delaySystemCount()`
- **数据来源**: 仅查询 `dq_new_station_delay` 表
- **修改前SQL查询**:
```sql
SELECT t2.system_name as systemName, count(distinct(t1.station_number)) as "count"
FROM dq_new_station_delay t1
LEFT JOIN bcs_standard_station t2 ON t1.station_number = t2.station_number 
AND t2.station_source = '原始'  -- 问题所在：过滤条件过于严格
WHERE t1.delay_state = 1
GROUP BY t2.system_name
```

### 关键差异点

1. **关联表不同**:
   - 公司侧: 关联 `bcs_sites` 表获取站点信息
   - 厂站侧: 关联 `bcs_standard_station` 表获取系统信息

2. **过滤条件不同**:
   - 公司侧: 无额外过滤条件，统计所有延迟测点
   - 厂站侧: 过滤 `station_source = '原始'`，排除了部分测点

3. **分组维度不同**:
   - 公司侧: 按站点分组 (`site_name`)
   - 厂站侧: 按系统分组 (`system_name`)

## 解决方案

采用最小改动原则，修改厂站统计接口的SQL查询条件，使其与公司侧统计保持一致的数据范围。

### 修改内容

**文件**: `src/main/java/com/dhcc/dsp/business/dataquality_new/dao/DataQualityStationDelayMapper.java`

**修改前**:
```sql
AND t2.station_source = '原始'
```

**修改后**:
```sql
AND t2.station_source != '关键测点'
```

### 修改理由

1. **数据范围一致性**: 与其他相关查询保持一致，如 `getDelayDetail` 方法已使用 `!= '关键测点'` 条件
2. **业务逻辑合理性**: 排除关键测点是合理的，但不应该只限制为'原始'测点
3. **最小改动**: 只修改一个过滤条件，不影响其他业务逻辑

## 预期效果

修改后，厂站统计接口将包含更多符合条件的延迟测点，使统计结果与公司侧统计更加一致。

## 验证方法

1. 重新部署应用
2. 调用两个接口，比较葛洲坝的统计数量
3. 确认数量差异是否缩小或消除

## 注意事项

- 此修改可能会导致厂站统计的数量增加，这是预期的行为
- 建议在测试环境先验证修改效果
- 如需进一步调整，可以考虑在业务层面统一两个接口的统计逻辑
