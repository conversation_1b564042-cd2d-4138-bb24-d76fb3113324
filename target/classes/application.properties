## 端口
#server.port=8084
### 多环境配置
spring.profiles.active=dm
##context path
server.servlet.context-path=/dataService
server.max-http-header-size=10000000

##
## json时间格式设置
spring.jackson.time-zone=GMT+8
spring.jackson.date-format=yyyy/MM/dd HH:mm:ss
##
api.version=service
##
## jwtp配置
#jwtp.store-type=1
#jwtp.max-token=3
#jwtp.path=/service/**
#jwtp.exclude-path=/service/user/login,/service/checkCAS,/service/getUserNameFromCasTicket,/service/publish/**
##日志配置
logging.level.com.dhcc.dsp=DEBUG
logging.level.org.springframework.security=DEBUG
logging.level.com.baomidou.mybatisplus=DEBUG
##上传文件大小配置
spring.servlet.multipart.max-file-size=110MB
spring.servlet.multipart.max-request-size=150MB
#流式返回超时时间
spring.mvc.async.request-timeout=-1
