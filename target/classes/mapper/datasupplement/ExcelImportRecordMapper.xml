<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dhcc.dsp.business.dao.datasupplement.ExcelImportRecordMapper">
    <resultMap id="BaseResultMap" type="com.dhcc.dsp.business.model.datasupplement.ExcelImportRecord">
        <id column="ID" jdbcType="VARCHAR" property="id"/>
        <result column="RESOURCE_ID" jdbcType="VARCHAR" property="resourceId"/>
        <result column="TBODY" jdbcType="VARCHAR" property="tbody"/>
        <result column="TABLE_NAME" jdbcType="VARCHAR" property="tableName"/>
        <result column="TABLE_DESC" jdbcType="VARCHAR" property="tableDesc"/>
        <result column="RECORD_TIME" jdbcType="TIMESTAMP" property="recordTime"/>
        <result column="CREATEBY" jdbcType="VARCHAR" property="createBy"/>
    </resultMap>
    <sql id="Base_Column_List">
    ID, RESOURCE_ID, TBODY, TABLE_NAME, TABLE_DESC, RECORD_TIME, CREATEBY
    </sql>
    <select id="selectList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from EXCEL_IMPORT_RECORD
        <where>
            <if test="tableDesc != null and tableDesc !=''">
                and TABLE_DESC like concat ('%',concat(#{tableDesc},'%'))
            </if>
        </where>
        order by RECORD_TIME desc
    </select>

    <select id="queryPageCount" parameterType="java.lang.String" resultType="java.lang.Integer">
        select
        COUNT(*)
        from EXCEL_IMPORT_RECORD
        <where>
            <if test="tableDesc != null and tableDesc !=''">
                and TABLE_DESC like concat ('%',concat(#{tableDesc},'%'))
            </if>
        </where>
    </select>

    <insert id="insert" parameterType="com.dhcc.dsp.business.model.datasupplement.ExcelImportRecord">
        insert into EXCEL_IMPORT_RECORD (ID, RESOURCE_ID, TBODY,
        TABLE_NAME, TABLE_DESC, RECORD_TIME, CREATEBY)
        values (#{id,jdbcType=VARCHAR}, #{resourceId,jdbcType=VARCHAR}, #{tbody,jdbcType=VARCHAR},
        #{tableName,jdbcType=VARCHAR},#{tableDesc,jdbcType=VARCHAR},#{recordTime,jdbcType=TIMESTAMP},#{createBy,jdbcType=VARCHAR})
    </insert>

    <delete id="deleteById" parameterType="java.lang.String">
        delete from EXCEL_IMPORT_RECORD
        where ID = #{id,jdbcType=VARCHAR}
    </delete>

    <select id="selectById" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from EXCEL_IMPORT_RECORD
        where ID = #{id,jdbcType=VARCHAR}
    </select>

    <select id="selectByTableDesc" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from EXCEL_IMPORT_RECORD
        where TABLE_DESC = #{tableDesc,jdbcType=VARCHAR}
    </select>

    <select id="selectByResourceIdAndTableName" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from EXCEL_IMPORT_RECORD
        <where>
            <if test="resourceId != null and resourceId !=''">
                and RESOURCE_ID = #{resourceId,jdbcType=VARCHAR}
            </if>
            <if test="tableName != null and tableName !=''">
                and TABLE_NAME = #{tableName,jdbcType=VARCHAR}
            </if>
        </where>
    </select>

    <delete id="deleteByResourceAndTableName" parameterType="java.lang.String">
        delete from EXCEL_IMPORT_RECORD
        <where>
            <if test="resourceId != null and resourceId !=''">
                and RESOURCE_ID = #{resourceId,jdbcType=VARCHAR}
            </if>
            <if test="tableName != null and tableName !=''">
                and TABLE_NAME = #{tableName,jdbcType=VARCHAR}
            </if>
        </where>
    </delete>
</mapper>