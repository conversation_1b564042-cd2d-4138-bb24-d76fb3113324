## 开发环境配置
##
## 数据源配置
spring.datasource.dynamic.primary=master
#spring.datasource.dynamic.datasource.master.url=*************************************************************************************************************************************
#spring.datasource.dynamic.datasource.master.username=avatar4
#spring.datasource.dynamic.datasource.master.password=XV13l/g1eMqmAYqW+yGXdILE3yIM/QKPdG5+P6AF+/srroAqwmewhQ9reCwHv3C/Br4VKLGE2EyYuyIjkDkdFQ==
#spring.datasource.dynamic.datasource.master.druid.public-key=MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBAKZRTl+PtnkJVJa7TOQR0OWuVtZQMbAvGJztNNxC+1YKB1OE0PAThdrNnjVltTFsqGbhASB7nInK5au7jcJiDsUCAwEAAQ==
#spring.datasource.dynamic.datasource.master.driver-class-name=com.mysql.jdbc.Driver

## 测试环境达梦数据库配置
spring.datasource.dynamic.datasource.master.url=jdbc:dm://172.16.15.185:3306/avatar?schema=AVATAR&clobAsString=true
spring.datasource.dynamic.datasource.master.username=avatar4
spring.datasource.dynamic.datasource.master.password=QPvDr/aQ8s7Rm9RMOhMTMCNsNeRx884oRhhiKKME/h/+lQZ8+uQ/JUgu1RMa2qF3JsxtJRtrbCCoYE7OCGSptg==
spring.datasource.dynamic.datasource.master.druid.public-key=MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBALQ0i0eCmadn1nxzrChMgn6WxxgXdhvFuGCyR2SgxF18WNwBQ4gmVpqWpGR/iBwr/RWvzSMcDPgAsIXd2OVOpNcCAwEAAQ==
spring.datasource.dynamic.datasource.master.driver-class-name=dm.jdbc.driver.DmDriver

spring.datasource.dynamic.datasource.master.type=com.alibaba.druid.pool.DruidDataSource
spring.datasource.dynamic.datasource.master.druid.validation-query=select 'x'

##日志配置
logging.level.com.dhcc.dsp=DEBUG
logging.level.org.springframework.security=DEBUG
logging.level.com.baomidou.mybatisplus=DEBUG

#流式返回超时时间
spring.mvc.async.request-timeout = -1
