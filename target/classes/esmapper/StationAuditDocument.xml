<?xml version="1.0" encoding="utf-8" ?>
<properties>
    <property name="createIndices">
        <![CDATA[
            {
                "settings": {
                    "index.max_result_window": 2000000,
                    "number_of_shards": 3,
                    "index.refresh_interval": "3s",
                    "index.mapping.ignore_malformed": true,
                    "analysis": {
                        "analyzer": {
                            "my_analyzer": {
                                "type": "ik_max_word"
                            }
                        }
                    }
                },
                "mappings": {
                    "station_audit_document": {
                        "dynamic_templates": [
                            {
                                "strings": {
                                    "match_mapping_type": "string",
                                    "mapping": {
                                        "type": "text",
                                        "analyzer": "my_analyzer",
                                        "fields": {
                                            "keyword": {
                                                "type": "keyword",
                                                "ignore_above": 256
                                            }
                                        }
                                    }
                                }
                            },
                            {
                                "numbers": {
                                    "match_mapping_type": "long",
                                    "mapping": {
                                        "type": "long"
                                    }
                                }
                            },
                            {
                                "decimals": {
                                    "match_mapping_type": "double",
                                    "mapping": {
                                        "type": "double"
                                    }
                                }
                            },
                            {
                                "booleans": {
                                    "match_mapping_type": "boolean",
                                    "mapping": {
                                        "type": "boolean"
                                    }
                                }
                            },
                            {
                                "dates": {
                                    "match_mapping_type": "date",
                                    "mapping": {
                                        "type": "date"
                                    }
                                }
                            },
                            {
                                "objects": {
                                    "match_mapping_type": "object",
                                    "mapping": {
                                        "type": "nested"
                                    }
                                }
                            }
                        ],
                        "dynamic": "true",
                        "properties": {
                            "siteId": {
                                "type": "keyword"
                            },
                            "siteName": {
                                "type": "text",
                                "analyzer": "my_analyzer",
                                "fields": {
                                    "keyword": {
                                        "type": "keyword",
                                        "ignore_above": 256
                                    }
                                }
                            },
                            "stationNumber": {
                                "type": "keyword"
                            },
                            "stationName": {
                                "type": "text",
                                "analyzer": "my_analyzer",
                                "fields": {
                                    "keyword": {
                                        "type": "keyword",
                                        "ignore_above": 256
                                    }
                                }
                            },
                            "bcsCHName": {
                                "type": "text",
                                "analyzer": "my_analyzer",
                                "fields": {
                                    "keyword": {
                                        "type": "keyword",
                                        "ignore_above": 256
                                    }
                                }
                            },
                            "systemName": {
                                "type": "text",
                                "analyzer": "my_analyzer",
                                "fields": {
                                    "keyword": {
                                        "type": "keyword",
                                        "ignore_above": 256
                                    }
                                }
                            },
                            "deviceName": {
                                "type": "text",
                                "analyzer": "my_analyzer",
                                "fields": {
                                    "keyword": {
                                        "type": "keyword",
                                        "ignore_above": 256
                                    }
                                }
                            },
                            "devicePath": {
                                "type": "text",
                                "analyzer": "my_analyzer",
                                "fields": {
                                    "keyword": {
                                        "type": "keyword",
                                        "ignore_above": 256
                                    }
                                }
                            },
                            "path": {
                                "type": "text",
                                "analyzer": "my_analyzer",
                                "fields": {
                                    "keyword": {
                                        "type": "keyword",
                                        "ignore_above": 256
                                    }
                                }
                            },
                            "propertyName": {
                                "type": "text",
                                "analyzer": "my_analyzer",
                                "fields": {
                                    "keyword": {
                                        "type": "keyword",
                                        "ignore_above": 256
                                    }
                                }
                            },
                            "required": {
                                "type": "keyword"
                            },
                            "upperValue": {
                                "type": "keyword"
                            },
                            "lowerValue": {
                                "type": "keyword"
                            },
                            "moreUpperValue": {
                                "type": "keyword"
                            },
                            "moreLowerValue": {
                                "type": "keyword"
                            },
                            "historySampleDensity": {
                                "type": "keyword"
                            },
                            "taskIds": {
                                "type": "keyword"
                            },
                            "taskNames": {
                                "type": "text",
                                "analyzer": "my_analyzer",
                                "fields": {
                                    "keyword": {
                                        "type": "keyword",
                                        "ignore_above": 256
                                    }
                                }
                            },
                            "taskStates": {
                                "type": "integer"
                            },
                            "libraryIds": {
                                "type": "keyword"
                            },
                            "libraryNames": {
                                "type": "text",
                                "analyzer": "my_analyzer",
                                "fields": {
                                    "keyword": {
                                        "type": "keyword",
                                        "ignore_above": 256
                                    }
                                }
                            },
                            "containsParamMap": {
                                "type": "nested",
                                "properties": {
                                    "key": {
                                        "type": "keyword"
                                    },
                                    "value": {
                                        "type": "boolean"
                                    }
                                }
                            },
                            "containsTaskMap": {
                                "type": "nested",
                                "properties": {
                                    "key": {
                                        "type": "keyword"
                                    },
                                    "value": {
                                        "type": "boolean"
                                    }
                                }
                            },
                            "auditParamDetailMap": {
                                "type": "nested"
                            },
                            "auditParamMap": {
                                "type": "nested"
                            },
                            "auditFieldsMap": {
                                "type": "nested"
                            },
                            "needAuditMap": {
                                "type": "nested",
                                "properties": {
                                    "key": {
                                        "type": "keyword"
                                    },
                                    "value": {
                                        "type": "boolean"
                                    }
                                }
                            },
                            "createTime": {
                                "type": "long"
                            },
                            "updateTime": {
                                "type": "long"
                            }
                        }
                    }
                }
            }
        ]]>
    </property>
</properties> 