##数据库配置
spring.datasource.dynamic.primary=master

## 测试环境达梦数据库配置
spring.datasource.dynamic.datasource.master.url=jdbc:dm://172.16.15.185:3306/avatar?schema=AVATAR&clobAsString=true
spring.datasource.dynamic.datasource.master.username=avatar4
spring.datasource.dynamic.datasource.master.password=QPvDr/aQ8s7Rm9RMOhMTMCNsNeRx884oRhhiKKME/h/+lQZ8+uQ/JUgu1RMa2qF3JsxtJRtrbCCoYE7OCGSptg==
spring.datasource.dynamic.datasource.master.druid.public-key=MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBALQ0i0eCmadn1nxzrChMgn6WxxgXdhvFuGCyR2SgxF18WNwBQ4gmVpqWpGR/iBwr/RWvzSMcDPgAsIXd2OVOpNcCAwEAAQ==
spring.datasource.dynamic.datasource.master.driver-class-name=dm.jdbc.driver.DmDriver
spring.datasource.dynamic.datasource.master.type=com.alibaba.druid.pool.DruidDataSource

## Mybatis-plus配置
mybatis-plus.mapper-locations=classpath:mapper/**/*Mapper.xml
mybatis-plus.typeAliasesPackage=com.dhcc.dsp.*.model
mybatis-plus.global-config.id-type=0
mybatis-plus.global-config.field-strategy=1
mybatis-plus.global-config.db-column-underline=true
mybatis-plus.global-config.logic-delete-value=1
mybatis-plus.global-config.logic-not-delete-value=0
mybatis-plus.configuration.map-underscore-to-camel-case=true
mybatis-plus.configuration.cache-enabled=false
mybatis-plus.configuration.call-setters-on-nulls=true
##
#连接池配置
spring.datasource.dynamic.druid.initial-size=5
spring.datasource.dynamic.druid.min-idle=0
spring.datasource.dynamic.druid.max-active=300
spring.datasource.dynamic.druid.max-wait=30000
spring.datasource.dynamic.druid.time-between-eviction-runs-millis=60000
spring.datasource.dynamic.druid.min-evictable-idle-time-millis=300000
spring.datasource.dynamic.druid.test-while-idle=false
spring.datasource.dynamic.druid.test-on-borrow=true
spring.datasource.dynamic.druid.test-on-return=false
##spring.datasource.druid.pool-prepared-statements=false
##spring.datasource.druid.max-pool-prepared-statement-per-connection-size=20
spring.datasource.dynamic.druid.remove-abandoned=false
spring.datasource.dynamic.druid.remove-abandoned-timeout=1800
spring.datasource.dynamic.druid.filters=stat
spring.datasource.dynamic.druid.stat-view-servlet.url-pattern=/druid/*
spring.datasource.dynamic.druid.stat-view-servlet.reset-enable=true
spring.datasource.dynamic.druid.stat-view-servlet.login-username=admin
spring.datasource.dynamic.druid.stat-view-servlet.login-password=admin
spring.datasource.dynamic.druid.aop-patterns=com.dhcc.dsp.*.service.*
